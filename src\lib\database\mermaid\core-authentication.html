<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Core Authentication & User Management</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Core Authentication & User Management</h1>
    <p><strong>Focus:</strong> User authentication, roles, permissions, family tree support</p>
    <div class="mermaid">
erDiagram
    %% ========================================
    %% CORE AUTHENTICATION & USER MANAGEMENT
    %% ========================================

    %% Core User System
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ USER_ROLES : assigned_to
    USERS ||--o{ USER_SESSIONS : creates
    USERS ||--o{ AUDIT_LOGS : generates
    USERS ||--o{ PASSWORD_RESETS : requests
    USERS ||--o{ USER_PREFERENCES : has

    %% Role-Based Access Control
    ROLES ||--|{ USER_ROLES : defines
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--|{ ROLE_PERMISSIONS : assigned_to
    PERMISSIONS ||--o{ USER_PERMISSIONS : overrides_for
    USERS ||--o{ USER_PERMISSIONS : has

    %% Family Tree Support - Multiple profiles per user account (Member role only)
    USERS ||--o{ FAMILY_MEMBERS : owns
    FAMILY_MEMBERS ||--|| USER_PROFILES : is

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    USERS {
        uuid id PK
        string email UK
        string password_hash
        enum status "active,inactive,suspended"
        enum user_type "individual,family"
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        json preferences
        boolean is_email_verified
        timestamp email_verified_at
    }

    USER_PROFILES {
        uuid id PK
        uuid user_id FK
        string first_name
        string last_name
        string display_name
        text bio
        string avatar_url
        string phone
        json social_links
        enum profile_type "personal,professional"
        timestamp created_at
        timestamp updated_at
    }

    FAMILY_MEMBERS {
        uuid id PK
        uuid user_id FK
        uuid profile_id FK
        string relationship "self,spouse,child,parent,sibling"
        boolean is_primary_contact
        timestamp added_at
    }

    ROLES {
        uuid id PK
        string name UK "member,moderator,admin"
        string description
        boolean is_active
        timestamp created_at
    }

    USER_ROLES {
        uuid id PK
        uuid user_id FK
        uuid role_id FK
        timestamp assigned_at
        uuid assigned_by FK
        timestamp valid_until
    }

    PERMISSIONS {
        uuid id PK
        string name UK
        string resource
        string action "create,read,update,delete,moderate"
        string description
    }

    USER_PERMISSIONS {
        uuid id PK
        uuid user_id FK
        uuid permission_id FK
        boolean is_granted
        timestamp granted_at
        uuid granted_by FK
    }

    PASSWORD_RESETS {
        uuid id PK
        uuid user_id FK
        string reset_token UK
        string secure_link_token UK
        timestamp link_expires_at
        timestamp requested_at
        timestamp expires_at
        boolean is_used
        timestamp used_at
        string ip_address
        string user_agent
        string reset_page_user_id_display
    }

    USER_SESSIONS {
        uuid id PK
        uuid user_id FK
        string session_token UK
        json device_info
        string ip_address
        timestamp created_at
        timestamp expires_at
    }

    USER_PREFERENCES {
        uuid id PK
        uuid user_id FK
        uuid domain_id FK
        enum preference_type "offer_support,seek_support,both"
        integer max_postings "configurable, default:5"
        json notification_settings
        boolean is_professional
        enum education_status "student,professional"
        timestamp created_at
        timestamp updated_at
    }

    AUDIT_LOGS {
        uuid id PK
        uuid user_id FK
        string action "login,logout,password_change,password_reset,create_posting,update_posting,delete_posting,express_interest,confirm_interest,posting_expired,help_request,help_response,rate_helper"
        string resource_type "user,posting,conversation,message,password_reset"
        uuid resource_id FK
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }
    </div>
</body>
</html>