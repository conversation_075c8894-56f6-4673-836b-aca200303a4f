erDiagram
    %% ========================================
    %% POSTINGS & CONTENT MANAGEMENT SYSTEM
    %% ========================================
    %% Enhanced for Gita Connect requirements:
    %% - Offer Support vs Seek Support postings
    %% - Moderation workflow
    %% - Interest expressions and help requests
    %% - Post-linked conversations

    %% Core Postings System (REPLACES generic POSTS)
    POSTINGS {
        uuid id PK
        uuid author_id FK
        string title
        text content
        enum posting_type "offer_support,seek_support"
        enum urgency_level "low,medium,high,critical"
        json tags
        timestamp expires_at
        enum status "draft,pending_review,approved,rejected,expired,active"
        uuid approved_by FK
        timestamp approved_at
        uuid rejected_by FK
        timestamp rejected_at
        text rejection_reason
        integer view_count
        integer interest_count
        boolean is_spam_flagged
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }

    POSTING_ATTACHMENTS {
        uuid id PK
        uuid posting_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    %% Categories and Domains for Postings
    POSTING_CATEGORIES {
        uuid id PK
        string name UK
        string description
        uuid parent_category_id FK
        string category_type "domain,help_type,urgency"
        boolean is_active
        timestamp created_at
    }

    %% Interest & Help Management (NEW - Core to Gita Connect)
    POSTING_INTERESTS {
        uuid id PK
        uuid posting_id FK
        uuid user_id FK
        text interest_message
        enum status "expressed,confirmed,connected,completed"
        timestamp expressed_at
        timestamp confirmed_at
        uuid confirmed_by FK
        timestamp created_at
        timestamp updated_at
    }

    INTEREST_CONFIRMATIONS {
        uuid id PK
        uuid interest_id FK
        uuid confirmed_by FK
        text confirmation_message
        timestamp confirmed_at
    }

    HELP_REQUESTS {
        uuid id PK
        uuid interest_id FK
        text request_details
        enum request_status "pending,responded,resolved,closed"
        enum urgency_level "low,medium,high,critical"
        timestamp requested_at
        timestamp resolved_at
        uuid resolved_by FK
        integer helper_rating
        text feedback
    }

    HELP_RESPONSES {
        uuid id PK
        uuid help_request_id FK
        uuid responder_id FK
        text response_message
        enum response_type "offer_help,provide_info,request_more_details"
        timestamp responded_at
    }

    HELPER_RATINGS {
        uuid id PK
        uuid help_request_id FK
        uuid helper_id FK
        uuid rater_id FK
        integer rating "1-5"
        text review
        timestamp rated_at
    }

    %% Enhanced Chat System
    CONVERSATIONS {
        uuid id PK
        string name
        text description
        boolean is_group
        uuid creator_id FK
        enum conversation_type "direct,group,post_linked"
        uuid linked_posting_id FK
        timestamp created_at
        timestamp last_activity
        timestamp expires_at
    }

    CONVERSATION_PARTICIPANTS {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        enum role "admin,member,observer"
        timestamp joined_at
        boolean is_active
        timestamp last_read_at
    }

    MESSAGES {
        uuid id PK
        uuid conversation_id FK
        uuid sender_id FK
        text content
        enum message_type "text,image,file,link"
        enum status "sent,delivered,read"
        boolean is_encrypted
        timestamp sent_at
        timestamp delivered_at
        timestamp read_at
        timestamp expires_at
    }

    MESSAGE_ATTACHMENTS {
        uuid id PK
        uuid message_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    MESSAGE_REACTIONS {
        uuid id PK
        uuid message_id FK
        uuid user_id FK
        string reaction_type "like,dislike,laugh,love,angry"
        timestamp reacted_at
    }

    USER_TYPING {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        timestamp started_at
        timestamp last_update
    }

    %% Notifications (Enhanced)
    NOTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string type "posting_interest,posting_approved,posting_rejected,message,mention"
        uuid reference_id FK
        json data
        boolean is_read
        timestamp created_at
        timestamp read_at
    }

    %% Moderation System (NEW - Required for Moderator workflow)
    MODERATION_DECISIONS {
        uuid id PK
        uuid posting_id FK
        uuid moderator_id FK
        enum decision "approve,reject,request_changes"
        text moderator_comments
        json requested_changes
        enum priority "low,medium,high"
        timestamp decided_at
        timestamp created_at
    }

    MODERATION_ACTIONS {
        uuid id PK
        uuid decision_id FK
        enum action_type "content_removed,user_warned,user_blocked,posting_hidden"
        text action_details
        timestamp executed_at
        uuid executed_by FK
    }

    %% Spam Detection (NEW)
    SPAM_FLAGS {
        uuid id PK
        uuid posting_id FK
        uuid flagged_by FK
        enum flag_type "spam,duplicate,inappropriate,scam"
        text flag_reason
        integer severity_score "1-10"
        timestamp flagged_at
    }

    USER_BLOCKS {
        uuid id PK
        uuid user_id FK
        uuid blocked_by FK
        enum block_type "posting,chat,messaging"
        text block_reason
        timestamp blocked_at
        timestamp expires_at
    }

    %% Analytics (NEW - Required for Moderator/Admin reporting)
    ANALYTICS_EVENTS {
        uuid id PK
        string event_type "posting_view,posting_interest,chat_message,help_request"
        uuid user_id FK
        uuid resource_id FK
        json event_data
        timestamp event_time
        string session_id
    }

    POSTING_ANALYTICS {
        uuid id PK
        uuid posting_id FK
        integer view_count
        integer interest_count
        integer response_count
        decimal avg_response_time_hours
        timestamp last_activity
        json category_stats
        timestamp created_at
        timestamp updated_at
    }

    %% Relationships
    USERS ||--o{ POSTINGS : creates
    USERS ||--o{ POSTING_INTERESTS : expresses
    USERS ||--o{ CONVERSATIONS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ USER_TYPING : indicates_typing
    USERS ||--o{ NOTIFICATIONS : receives
    USERS ||--o{ MODERATION_DECISIONS : makes

    POSTINGS ||--o{ POSTING_ATTACHMENTS : contains
    POSTINGS ||--o{ POSTING_INTERESTS : receives
    POSTINGS ||--o{ POSTING_CATEGORIES : categorized_as
    POSTINGS ||--o{ MODERATION_DECISIONS : reviewed_by
    POSTINGS ||--o{ SPAM_FLAGS : flagged_as
    POSTINGS ||--o{ POSTING_ANALYTICS : analyzed_in

    POSTING_INTERESTS ||--o{ INTEREST_CONFIRMATIONS : receives
    POSTING_INTERESTS ||--o{ HELP_REQUESTS : creates
    HELP_REQUESTS ||--o{ HELP_RESPONSES : receives
    HELP_REQUESTS ||--o{ HELPER_RATINGS : rated_by

    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : has
    CONVERSATIONS ||--o{ MESSAGES : contains
    CONVERSATIONS ||--o{ USER_TYPING : tracks_typing
    CONVERSATIONS ||--o{ POSTING_CONVERSATIONS : linked_to

    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : contains
    MESSAGES ||--o{ MESSAGE_REACTIONS : has

    MODERATION_DECISIONS ||--o{ MODERATION_ACTIONS : triggers