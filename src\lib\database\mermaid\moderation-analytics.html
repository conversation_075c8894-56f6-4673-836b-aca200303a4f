<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moderation & Analytics System</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Moderation & Analytics System</h1>
    <p><strong>Focus:</strong> Content moderation, spam detection, analytics, reporting</p>
    <div class="mermaid">
erDiagram
    %% ========================================
    %% MODERATION & ADMINISTRATION
    %% ========================================

    %% Moderation System
    USERS ||--o{ MODERATION_DECISIONS : makes
    MODERATION_DECISIONS ||--o{ MODERATION_ACTIONS : triggers
    MODERATION_DECISIONS ||--o{ AUDIT_LOGS : logged_in

    %% Spam Detection & User Management
    POSTINGS ||--o{ SPAM_FLAGS : flagged_as
    USERS ||--o{ USER_BLOCKS : blocked_by
    POSTINGS ||--o{ DUPLICATE_POSTINGS : detected_as
    POSTINGS ||--o{ EXPIRED_POSTINGS : monitored_as
    POSTINGS ||--o{ ANALYTICS_EVENTS : tracked_in
    MODERATORS ||--o{ MODERATOR_NOTIFICATIONS : receive

    %% ========================================
    %% ANALYTICS & REPORTING
    %% ========================================

    POSTINGS ||--o{ POSTING_ANALYTICS : analyzed_in
    USERS ||--o{ USER_ANALYTICS : tracked_in
    CONVERSATIONS ||--o{ CONVERSATION_ANALYTICS : analyzed_in
    ANALYTICS_EVENTS ||--o{ ANALYTICS_REPORTS : generates

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    %% Moderation System
    MODERATION_DECISIONS {
        uuid id PK
        uuid posting_id FK
        uuid moderator_id FK
        enum decision "approve,reject,request_changes"
        text moderator_comments
        json requested_changes
        enum priority "low,medium,high"
        timestamp decided_at
        timestamp created_at
    }

    MODERATION_ACTIONS {
        uuid id PK
        uuid decision_id FK
        enum action_type "content_removed,user_warned,user_blocked,posting_hidden"
        text action_details
        timestamp executed_at
        uuid executed_by FK
    }

    SPAM_FLAGS {
        uuid id PK
        uuid posting_id FK
        uuid flagged_by FK
        enum flag_type "spam,duplicate,inappropriate,scam"
        text flag_reason
        integer severity_score "1-10"
        timestamp flagged_at
    }

    USER_BLOCKS {
        uuid id PK
        uuid user_id FK
        uuid blocked_by FK
        enum block_type "posting,chat,messaging"
        text block_reason
        timestamp blocked_at
        timestamp expires_at
    }

    DUPLICATE_POSTINGS {
        uuid id PK
        uuid original_posting_id FK
        uuid duplicate_posting_id FK
        enum detection_method "manual,automated"
        decimal similarity_score "0-1"
        uuid detected_by FK
        timestamp detected_at
        enum action_taken "flagged,removed,merged"
    }

    EXPIRED_POSTINGS {
        uuid id PK
        uuid posting_id FK
        enum expiry_reason "date_expired,auto_30_days"
        timestamp expired_at
        uuid monitored_by FK
        timestamp monitored_at
    }

    MODERATOR_NOTIFICATIONS {
        uuid id PK
        uuid moderator_id FK
        string notification_type "new_posting,duplicate_detected,spam_flagged"
        uuid reference_id FK
        json notification_data
        boolean is_read
        timestamp created_at
        timestamp read_at
    }

    %% Analytics & Reporting
    ANALYTICS_EVENTS {
        uuid id PK
        string event_type "posting_view,posting_interest,chat_message,help_request"
        uuid user_id FK
        uuid resource_id FK
        json event_data
        timestamp event_time
        string session_id
    }

    POSTING_ANALYTICS {
        uuid id PK
        uuid posting_id FK
        integer view_count
        integer interest_count
        integer response_count
        decimal avg_response_time_hours
        timestamp last_activity
        json category_stats
        timestamp created_at
        timestamp updated_at
    }

    USER_ANALYTICS {
        uuid id PK
        uuid user_id FK
        integer total_postings
        integer active_postings
        integer total_interests
        integer successful_connections
        decimal avg_rating_received
        timestamp last_activity
        json domain_activity
        timestamp created_at
        timestamp updated_at
    }

    CONVERSATION_ANALYTICS {
        uuid id PK
        uuid conversation_id FK
        integer message_count
        integer participant_count
        timestamp last_message_at
        decimal avg_response_time_minutes
        json engagement_metrics
        timestamp created_at
        timestamp updated_at
    }

    ANALYTICS_REPORTS {
        uuid id PK
        string report_type "posting_summary,user_activity,moderation_summary,category_response_rates,posting_success_metrics"
        json report_data
        date report_date
        uuid generated_by FK
        timestamp generated_at
    }

    CATEGORY_ANALYTICS {
        uuid id PK
        uuid domain_id FK
        string category_name
        integer total_postings
        integer offer_support_postings
        integer seek_support_postings
        integer postings_with_responses
        integer successful_connections
        decimal avg_response_time_days
        timestamp last_updated
    }
    </div>
</body>
</html>