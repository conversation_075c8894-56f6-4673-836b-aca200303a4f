<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat & Communication System</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Chat & Communication System</h1>
    <p><strong>Focus:</strong> Real-time messaging, post-linked conversations, notifications</p>
    <div class="mermaid">
erDiagram
    %% ========================================
    %% CHAT & MESSAGING SYSTEM
    %% ========================================

    %% Core Chat System
    USERS ||--o{ CONVERSATIONS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ USER_TYPING : indicates_typing
    USERS ||--o{ NOTIFICATIONS : receives

    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : has
    CONVERSATIONS ||--o{ MESSAGES : contains
    CONVERSATIONS ||--o{ USER_TYPING : tracks_typing
    CONVERSATIONS ||--o{ POSTING_CONVERSATIONS : linked_to

    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : contains
    MESSAGES ||--o{ MESSAGE_REACTIONS : has

    %% Post-Linked Conversations
    POSTINGS ||--o{ POSTING_CONVERSATIONS : initiates
    POSTING_CONVERSATIONS ||--|| CONVERSATIONS : creates

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    CONVERSATIONS {
        uuid id PK
        string name
        text description
        boolean is_group
        uuid creator_id FK
        enum conversation_type "direct,group,post_linked"
        uuid linked_posting_id FK
        timestamp created_at
        timestamp last_activity
        timestamp expires_at
    }

    CONVERSATION_PARTICIPANTS {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        enum role "admin,member,observer"
        timestamp joined_at
        boolean is_active
        timestamp last_read_at
    }

    MESSAGES {
        uuid id PK
        uuid conversation_id FK
        uuid sender_id FK
        text content
        enum message_type "text,image,file,link"
        enum status "sent,delivered,read"
        boolean is_encrypted
        timestamp sent_at
        timestamp delivered_at
        timestamp read_at
        timestamp expires_at
    }

    MESSAGE_ATTACHMENTS {
        uuid id PK
        uuid message_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    MESSAGE_REACTIONS {
        uuid id PK
        uuid message_id FK
        uuid user_id FK
        string reaction_type "like,dislike,laugh,love,angry"
        timestamp reacted_at
    }

    USER_TYPING {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        timestamp started_at
        timestamp last_update
    }

    CONVERSATION_IDLE_TIMEOUT {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        timestamp idle_started_at
        boolean prompt_shown
        timestamp prompt_at
        enum user_response "continue,exit"
        timestamp responded_at
    }

    NOTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string type "posting_interest,posting_approved,posting_rejected,message,mention"
        uuid reference_id FK
        json data
        boolean is_read
        timestamp created_at
        timestamp read_at
    }

    POSTING_CONVERSATIONS {
        uuid id PK
        uuid posting_id FK
        uuid conversation_id FK
        timestamp created_at
    }
    </div>
</body>
</html>