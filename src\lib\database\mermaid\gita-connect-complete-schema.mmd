erDiagram
    %% ========================================
    %% CORE USER MANAGEMENT & AUTHENTICATION
    %% ========================================
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ USER_ROLES : assigned_to
    USERS ||--o{ USER_SESSIONS : creates
    USERS ||--o{ AUDIT_LOGS : generates
    USERS ||--o{ PASSWORD_RESETS : requests
    USERS ||--o{ USER_PREFERENCES : has

    ROLES ||--|{ USER_ROLES : defines
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--|{ ROLE_PERMISSIONS : assigned_to
    PERMISSIONS ||--o{ USER_PERMISSIONS : overrides_for
    USERS ||--o{ USER_PERMISSIONS : has

    %% ========================================
    %% ALUMNI PROFILES & FAMILY TREE
    %% ========================================
    USER_PROFILES ||--|| ALUMNI_PROFILES : extends
    ALUMNI_PROFILES ||--o{ EDUCATION_HISTORY : has
    ALUMNI_PROFILES ||--o{ CAREER_HISTORY : has
    ALUMNI_PROFILES ||--o{ ACHIEVEMENTS : has
    ALUMNI_PROFILES ||--o{ ALUMNI_SKILLS : has
    ALUMNI_PROFILES ||--o{ ALUMNI_DOMAINS : specializes_in

    %% Family Tree Support - Multiple profiles can share credentials
    USERS ||--o{ FAMILY_MEMBERS : owns
    FAMILY_MEMBERS ||--|| USER_PROFILES : is

    %% ========================================
    %% POSTINGS & CONTENT MANAGEMENT
    %% ========================================
    USERS ||--o{ POSTINGS : creates
    POSTINGS ||--o{ POSTING_ATTACHMENTS : contains
    POSTINGS ||--o{ POSTING_INTERESTS : receives
    POSTINGS ||--o{ POSTING_CATEGORIES : categorized_as
    POSTINGS ||--o{ POSTING_DOMAINS : belongs_to
    POSTINGS ||--o{ MODERATION_DECISIONS : reviewed_by
    POSTINGS ||--o{ AUDIT_LOGS : tracked_in

    %% Interest & Help Requests
    USERS ||--o{ POSTING_INTERESTS : expresses
    POSTING_INTERESTS ||--o{ INTEREST_CONFIRMATIONS : receives
    POSTING_INTERESTS ||--o{ HELP_REQUESTS : creates
    HELP_REQUESTS ||--o{ HELP_RESPONSES : receives
    HELP_REQUESTS ||--o{ HELPER_RATINGS : rated_by

    %% ========================================
    %% CHAT & MESSAGING SYSTEM
    %% ========================================
    USERS ||--o{ CONVERSATIONS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ USER_TYPING : indicates_typing
    USERS ||--o{ NOTIFICATIONS : receives

    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : has
    CONVERSATIONS ||--o{ MESSAGES : contains
    CONVERSATIONS ||--o{ USER_TYPING : tracks_typing
    CONVERSATIONS ||--o{ POSTING_CONVERSATIONS : linked_to

    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : contains
    MESSAGES ||--o{ MESSAGE_REACTIONS : has

    %% Post-Linked Conversations
    POSTINGS ||--o{ POSTING_CONVERSATIONS : initiates
    POSTING_CONVERSATIONS ||--|| CONVERSATIONS : creates

    %% ========================================
    %% MODERATION & ADMINISTRATION
    %% ========================================
    USERS ||--o{ MODERATION_DECISIONS : makes
    MODERATION_DECISIONS ||--o{ MODERATION_ACTIONS : triggers
    MODERATION_DECISIONS ||--o{ AUDIT_LOGS : logged_in

    %% Spam Detection & Analytics
    POSTINGS ||--o{ SPAM_FLAGS : flagged_as
    USERS ||--o{ USER_BLOCKS : blocked_by
    POSTINGS ||--o{ ANALYTICS_EVENTS : tracked_in

    %% ========================================
    %% ANALYTICS & REPORTING
    %% ========================================
    POSTINGS ||--o{ POSTING_ANALYTICS : analyzed_in
    USERS ||--o{ USER_ANALYTICS : tracked_in
    CONVERSATIONS ||--o{ CONVERSATION_ANALYTICS : analyzed_in
    ANALYTICS_EVENTS ||--o{ ANALYTICS_REPORTS : generates

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    %% Core Users & Authentication
    USERS {
        uuid id PK
        string email UK
        string password_hash
        enum status "active,inactive,suspended"
        enum user_type "individual,family"
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        json preferences
        boolean is_email_verified
        timestamp email_verified_at
    }

    USER_PROFILES {
        uuid id PK
        uuid user_id FK
        string first_name
        string last_name
        string display_name
        text bio
        string avatar_url
        string phone
        json social_links
        enum profile_type "personal,professional"
        timestamp created_at
        timestamp updated_at
    }

    FAMILY_MEMBERS {
        uuid id PK
        uuid user_id FK
        uuid profile_id FK
        string relationship "self,spouse,child,parent,sibling"
        boolean is_primary_contact
        timestamp added_at
    }

    ROLES {
        uuid id PK
        string name UK "member,moderator,admin"
        string description
        boolean is_active
        timestamp created_at
    }

    USER_ROLES {
        uuid id PK
        uuid user_id FK
        uuid role_id FK
        timestamp assigned_at
        uuid assigned_by FK
        timestamp valid_until
    }

    PERMISSIONS {
        uuid id PK
        string name UK
        string resource
        string action "create,read,update,delete,moderate"
        string description
    }

    USER_PERMISSIONS {
        uuid id PK
        uuid user_id FK
        uuid permission_id FK
        boolean is_granted
        timestamp granted_at
        uuid granted_by FK
    }

    %% Password Management
    PASSWORD_RESETS {
        uuid id PK
        uuid user_id FK
        string reset_token UK
        timestamp requested_at
        timestamp expires_at
        boolean is_used
        timestamp used_at
        string ip_address
        string user_agent
    }

    %% User Preferences
    USER_PREFERENCES {
        uuid id PK
        uuid user_id FK
        uuid domain_id FK
        enum preference_type "offer_support,seek_support,both"
        integer max_postings "default:5"
        json notification_settings
        boolean is_professional
        enum education_status "student,professional"
        timestamp created_at
        timestamp updated_at
    }

    %% Alumni-Specific
    ALUMNI_PROFILES {
        uuid id PK
        uuid user_profile_id FK
        string student_id UK
        date graduation_date
        string degree_type
        string major
        string minor
        decimal gpa
        enum alumni_status "active,inactive,pending"
        enum mentor_status "available,unavailable,mentoring"
        json custom_fields
        timestamp created_at
        timestamp updated_at
    }

    EDUCATION_HISTORY {
        uuid id PK
        uuid alumni_profile_id FK
        string institution_name
        string degree_type
        string field_of_study
        date start_date
        date end_date
        decimal gpa
        text description
        boolean is_primary
        timestamp created_at
    }

    CAREER_HISTORY {
        uuid id PK
        uuid alumni_profile_id FK
        string company_name
        string position_title
        text job_description
        date start_date
        date end_date
        string location
        string industry
        decimal salary_range_min
        decimal salary_range_max
        boolean is_current
        enum employment_type "full_time,part_time,contract,internship"
        timestamp created_at
    }

    ACHIEVEMENTS {
        uuid id PK
        uuid alumni_profile_id FK
        string title
        text description
        date achieved_date
        string issuer
        string achievement_type "certification,award,publication,project"
        timestamp created_at
    }

    SKILLS {
        uuid id PK
        string name UK
        string category
        boolean is_active
        timestamp created_at
    }

    ALUMNI_SKILLS {
        uuid id PK
        uuid alumni_profile_id FK
        uuid skill_id FK
        enum proficiency_level "beginner,intermediate,advanced,expert"
        timestamp acquired_at
        timestamp last_used
    }

    %% Domains & Categories
    DOMAINS {
        uuid id PK
        string name UK
        string description
        string color_code
        json metadata
        boolean is_active
        timestamp created_at
    }

    ALUMNI_DOMAINS {
        uuid id PK
        uuid alumni_profile_id FK
        uuid domain_id FK
        enum expertise_level "beginner,intermediate,advanced,expert"
        integer years_experience
        boolean is_offering_support
        boolean is_seeking_support
        timestamp created_at
    }

    POSTING_CATEGORIES {
        uuid id PK
        string name UK
        string description
        uuid parent_category_id FK
        string category_type "domain,help_type,urgency"
        boolean is_active
        timestamp created_at
    }

    %% Core Postings System
    POSTINGS {
        uuid id PK
        uuid author_id FK
        string title
        text content
        enum posting_type "offer_support,seek_support"
        enum urgency_level "low,medium,high,critical"
        json tags
        timestamp expires_at
        enum status "draft,pending_review,approved,rejected,expired,active"
        uuid approved_by FK
        timestamp approved_at
        uuid rejected_by FK
        timestamp rejected_at
        text rejection_reason
        integer view_count
        integer interest_count
        boolean is_spam_flagged
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }

    POSTING_ATTACHMENTS {
        uuid id PK
        uuid posting_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    %% Interest & Help Management
    POSTING_INTERESTS {
        uuid id PK
        uuid posting_id FK
        uuid user_id FK
        text interest_message
        enum status "expressed,confirmed,connected,completed"
        timestamp expressed_at
        timestamp confirmed_at
        uuid confirmed_by FK
        timestamp created_at
        timestamp updated_at
    }

    INTEREST_CONFIRMATIONS {
        uuid id PK
        uuid interest_id FK
        uuid confirmed_by FK
        text confirmation_message
        timestamp confirmed_at
    }

    HELP_REQUESTS {
        uuid id PK
        uuid interest_id FK
        text request_details
        enum request_status "pending,responded,resolved,closed"
        enum urgency_level "low,medium,high,critical"
        timestamp requested_at
        timestamp resolved_at
        uuid resolved_by FK
        integer helper_rating
        text feedback
    }

    HELP_RESPONSES {
        uuid id PK
        uuid help_request_id FK
        uuid responder_id FK
        text response_message
        enum response_type "offer_help,provide_info,request_more_details"
        timestamp responded_at
    }

    HELPER_RATINGS {
        uuid id PK
        uuid help_request_id FK
        uuid helper_id FK
        uuid rater_id FK
        integer rating "1-5"
        text review
        timestamp rated_at
    }

    %% Chat & Communication
    CONVERSATIONS {
        uuid id PK
        string name
        text description
        boolean is_group
        uuid creator_id FK
        enum conversation_type "direct,group,post_linked"
        uuid linked_posting_id FK
        timestamp created_at
        timestamp last_activity
        timestamp expires_at
    }

    CONVERSATION_PARTICIPANTS {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        enum role "admin,member,observer"
        timestamp joined_at
        boolean is_active
        timestamp last_read_at
    }

    MESSAGES {
        uuid id PK
        uuid conversation_id FK
        uuid sender_id FK
        text content
        enum message_type "text,image,file,link"
        enum status "sent,delivered,read"
        boolean is_encrypted
        timestamp sent_at
        timestamp delivered_at
        timestamp read_at
        timestamp expires_at
    }

    MESSAGE_ATTACHMENTS {
        uuid id PK
        uuid message_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    MESSAGE_REACTIONS {
        uuid id PK
        uuid message_id FK
        uuid user_id FK
        string reaction_type "like,dislike,laugh,love,angry"
        timestamp reacted_at
    }

    USER_TYPING {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        timestamp started_at
        timestamp last_update
    }

    %% Notifications
    NOTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string type "posting_interest,posting_approved,posting_rejected,message,mention"
        uuid reference_id FK
        json data
        boolean is_read
        timestamp created_at
        timestamp read_at
    }

    %% Moderation System
    MODERATION_DECISIONS {
        uuid id PK
        uuid posting_id FK
        uuid moderator_id FK
        enum decision "approve,reject,request_changes"
        text moderator_comments
        json requested_changes
        enum priority "low,medium,high"
        timestamp decided_at
        timestamp created_at
    }

    MODERATION_ACTIONS {
        uuid id PK
        uuid decision_id FK
        enum action_type "content_removed,user_warned,user_blocked,posting_hidden"
        text action_details
        timestamp executed_at
        uuid executed_by FK
    }

    SPAM_FLAGS {
        uuid id PK
        uuid posting_id FK
        uuid flagged_by FK
        enum flag_type "spam,duplicate,inappropriate,scam"
        text flag_reason
        integer severity_score "1-10"
        timestamp flagged_at
    }

    USER_BLOCKS {
        uuid id PK
        uuid user_id FK
        uuid blocked_by FK
        enum block_type "posting,chat,messaging"
        text block_reason
        timestamp blocked_at
        timestamp expires_at
    }

    %% Audit & Analytics
    AUDIT_LOGS {
        uuid id PK
        uuid user_id FK
        string action "login,logout,create_posting,update_posting,delete_posting"
        string resource_type "user,posting,conversation,message"
        uuid resource_id FK
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }

    ANALYTICS_EVENTS {
        uuid id PK
        string event_type "posting_view,posting_interest,chat_message,help_request"
        uuid user_id FK
        uuid resource_id FK
        json event_data
        timestamp event_time
        string session_id
    }

    POSTING_ANALYTICS {
        uuid id PK
        uuid posting_id FK
        integer view_count
        integer interest_count
        integer response_count
        decimal avg_response_time_hours
        timestamp last_activity
        json category_stats
        timestamp created_at
        timestamp updated_at
    }

    USER_ANALYTICS {
        uuid id PK
        uuid user_id FK
        integer total_postings
        integer active_postings
        integer total_interests
        integer successful_connections
        decimal avg_rating_received
        timestamp last_activity
        json domain_activity
        timestamp created_at
        timestamp updated_at
    }

    CONVERSATION_ANALYTICS {
        uuid id PK
        uuid conversation_id FK
        integer message_count
        integer participant_count
        timestamp last_message_at
        decimal avg_response_time_minutes
        json engagement_metrics
        timestamp created_at
        timestamp updated_at
    }

    ANALYTICS_REPORTS {
        uuid id PK
        string report_type "posting_summary,user_activity,moderation_summary"
        json report_data
        date report_date
        uuid generated_by FK
        timestamp generated_at
    }