<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Postings & Content Management</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Postings & Content Management</h1>
    <p><strong>Focus:</strong> Specialized posting system for offer/seek support, interest expressions, help requests</p>
    <div class="mermaid">
erDiagram
    %% ========================================
    %% POSTINGS & CONTENT MANAGEMENT
    %% ========================================

    %% Core Postings System
    USERS ||--o{ POSTINGS : creates
    POSTINGS ||--o{ POSTING_ATTACHMENTS : contains
    POSTINGS ||--o{ POSTING_INTERESTS : receives
    POSTINGS ||--o{ POSTING_CATEGORIES : categorized_as
    POSTINGS ||--o{ POSTING_DOMAINS : belongs_to
    POSTINGS ||--o{ MODERATION_DECISIONS : reviewed_by
    POSTINGS ||--o{ AUDIT_LOGS : tracked_in

    %% Interest & Help Requests
    USERS ||--o{ POSTING_INTERESTS : expresses
    POSTING_INTERESTS ||--o{ INTEREST_CONFIRMATIONS : receives
    POSTING_INTERESTS ||--o{ HELP_REQUESTS : creates
    HELP_REQUESTS ||--o{ HELP_RESPONSES : receives
    HELP_REQUESTS ||--o{ HELPER_RATINGS : rated_by

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    POSTING_CATEGORIES {
        uuid id PK
        string name UK
        string description
        uuid parent_category_id FK
        string category_type "domain,help_type,urgency"
        boolean is_active
        timestamp created_at
    }

    POSTINGS {
        uuid id PK
        uuid author_id FK
        string title
        text content
        enum posting_type "offer_support,seek_support"
        enum urgency_level "low,medium,high,critical"
        json tags
        timestamp expires_at "max(posting_expiry_date, created_at + 30 days)"
        enum status "draft,pending_review,approved,rejected,expired,active"
        uuid approved_by FK
        timestamp approved_at
        uuid rejected_by FK
        timestamp rejected_at
        text rejection_reason
        integer view_count
        integer interest_count
        boolean is_spam_flagged
        timestamp published_at
        timestamp created_at
        timestamp updated_at
        string contact_name
        string contact_phone
        string contact_country
        string contact_email
    }

    POSTING_ATTACHMENTS {
        uuid id PK
        uuid posting_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }

    POSTING_INTERESTS {
        uuid id PK
        uuid posting_id FK
        uuid user_id FK
        text interest_message
        enum status "expressed,confirmed,connected,completed"
        timestamp expressed_at
        timestamp confirmed_at
        uuid confirmed_by FK
        timestamp created_at
        timestamp updated_at
    }

    INTEREST_CONFIRMATIONS {
        uuid id PK
        uuid interest_id FK
        uuid confirmed_by FK
        text confirmation_message
        timestamp confirmed_at
    }

    HELP_REQUESTS {
        uuid id PK
        uuid interest_id FK
        text request_details
        enum request_status "pending,responded,resolved,closed"
        enum urgency_level "low,medium,high,critical"
        timestamp requested_at
        timestamp resolved_at
        uuid resolved_by FK
        integer helper_rating
        text feedback
    }

    HELP_RESPONSES {
        uuid id PK
        uuid help_request_id FK
        uuid responder_id FK
        text response_message
        enum response_type "offer_help,provide_info,request_more_details"
        timestamp responded_at
    }

    HELPER_RATINGS {
        uuid id PK
        uuid help_request_id FK
        uuid helper_id FK
        uuid rater_id FK
        integer rating "1-5"
        text review
        timestamp rated_at
    }
    </div>
</body>
</html>