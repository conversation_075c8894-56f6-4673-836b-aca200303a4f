<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alumni Domain Expertise & Profiles</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Alumni Domain Expertise & Profiles</h1>
    <p><strong>Focus:</strong> Alumni profiles, domain expertise, skills, education, career history</p>
    <div class="mermaid">
erDiagram
    %% ========================================
    %% ALUMNI PROFILES & DOMAIN EXPERTISE
    %% ========================================

    %% Core Alumni Profile
    USER_PROFILES ||--|| ALUMNI_PROFILES : extends
    ALUMNI_PROFILES ||--o{ EDUCATION_HISTORY : has
    ALUMNI_PROFILES ||--o{ CAREER_HISTORY : has
    ALUMNI_PROFILES ||--o{ ACHIEVEMENTS : has
    ALUMNI_PROFILES ||--o{ ALUMNI_SKILLS : has
    ALUMNI_PROFILES ||--o{ ALUMNI_DOMAINS : specializes_in

    %% Skills Management
    SKILLS ||--o{ ALUMNI_SKILLS : used_by

    %% Domain Expertise
    DOMAINS ||--o{ ALUMNI_DOMAINS : categorizes

    %% ========================================
    %% ENTITY DEFINITIONS
    %% ========================================

    ALUMNI_PROFILES {
        uuid id PK
        uuid user_profile_id FK
        string student_id UK
        date graduation_date
        string degree_type
        string major
        string minor
        decimal gpa
        enum alumni_status "active,inactive,pending"
        enum mentor_status "available,unavailable,mentoring"
        json custom_fields
        timestamp created_at
        timestamp updated_at
    }

    EDUCATION_HISTORY {
        uuid id PK
        uuid alumni_profile_id FK
        string institution_name
        string degree_type
        string field_of_study
        date start_date
        date end_date
        decimal gpa
        text description
        boolean is_primary
        timestamp created_at
    }

    CAREER_HISTORY {
        uuid id PK
        uuid alumni_profile_id FK
        string company_name
        string position_title
        text job_description
        date start_date
        date end_date
        string location
        string industry
        decimal salary_range_min
        decimal salary_range_max
        boolean is_current
        enum employment_type "full_time,part_time,contract,internship"
        timestamp created_at
    }

    ACHIEVEMENTS {
        uuid id PK
        uuid alumni_profile_id FK
        string title
        text description
        date achieved_date
        string issuer
        string achievement_type "certification,award,publication,project"
        timestamp created_at
    }

    SKILLS {
        uuid id PK
        string name UK
        string category
        boolean is_active
        timestamp created_at
    }

    ALUMNI_SKILLS {
        uuid id PK
        uuid alumni_profile_id FK
        uuid skill_id FK
        enum proficiency_level "beginner,intermediate,advanced,expert"
        timestamp acquired_at
        timestamp last_used
    }

    DOMAINS {
        uuid id PK
        string name UK
        string description
        uuid parent_domain_id FK
        string color_code
        enum domain_level "top_level,sub_category,specialization"
        json metadata
        boolean is_active
        timestamp created_at
    }

    ALUMNI_DOMAINS {
        uuid id PK
        uuid alumni_profile_id FK
        uuid domain_id FK
        enum expertise_level "beginner,intermediate,advanced,expert"
        integer years_experience
        boolean is_offering_support
        boolean is_seeking_support
        timestamp created_at
    }
    </div>
</body>
</html>