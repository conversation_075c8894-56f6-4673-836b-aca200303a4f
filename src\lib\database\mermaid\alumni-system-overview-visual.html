<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Alumni System Database Schema</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Complete Alumni System Database Schema</h1>
    <p><strong>Key Features:</strong></p>
    <ul>
        <li>Multi-role Support - Flexible permission system</li>
        <li>Rich Profiles - Comprehensive alumni information with skills and mentor status</li>
        <li>Enhanced Messaging - Group chats, message status, encryption, typing indicators</li>
        <li>Social Features - Posts, comments, notifications</li>
        <li>Event Management - Complete event lifecycle</li>
        <li>Professional Networking - Job postings and connections</li>
        <li>Audit Trail - Complete activity logging</li>
    </ul>
    <div class="mermaid">
erDiagram
    %% Core User Management
    USERS ||--o{ USER_PROFILES : has
    USERS ||--o{ USER_ROLES : has
    USERS ||--o{ USER_SESSIONS : creates
    USERS ||--o{ AUDIT_LOGS : generates
    ROLES ||--|{ USER_ROLES : assigned_to
    ROLES ||--o{ ROLE_PERMISSIONS : has
    PERMISSIONS ||--|{ ROLE_PERMISSIONS : assigned_to
    PERMISSIONS ||--o{ USER_PERMISSIONS : overrides_for
    USERS ||--o{ USER_PERMISSIONS : has

    %% Alumni-Specific
    USER_PROFILES ||--|| ALUMNI_PROFILES : extends
    ALUMNI_PROFILES ||--o{ EDUCATION_HISTORY : has
    ALUMNI_PROFILES ||--o{ CAREER_HISTORY : has
    ALUMNI_PROFILES ||--o{ ACHIEVEMENTS : has
    ALUMNI_PROFILES ||--o{ ALUMNI_SKILLS : has
    SKILLS ||--o{ ALUMNI_SKILLS : used_by
    DOMAINS ||--o{ ALUMNI_PROFILES : categorizes

    %% Content & Communication
    USERS ||--o{ POSTS : creates
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ CONVERSATIONS : creates
    USERS ||--o{ CONVERSATION_PARTICIPANTS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ USER_TYPING : indicates_typing
    USERS ||--o{ NOTIFICATIONS : receives
    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : has
    CONVERSATIONS ||--o{ MESSAGES : contains
    CONVERSATIONS ||--o{ USER_TYPING : tracks_typing
    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : contains
    POSTS ||--o{ COMMENTS : has
    POSTS ||--o{ POST_ATTACHMENTS : contains

    %% Events & Activities
    USERS ||--o{ EVENTS : organizes
    USERS ||--o{ EVENT_REGISTRATIONS : registers
    EVENTS ||--o{ EVENT_REGISTRATIONS : has

    %% Professional Network
    USERS ||--o{ CONNECTIONS : initiates
    USERS ||--o{ CONNECTIONS : receives
    USERS ||--o{ JOB_POSTINGS : creates
    USERS ||--o{ JOB_APPLICATIONS : submits
    JOB_POSTINGS ||--o{ JOB_APPLICATIONS : receives

    %% Entity Definitions
    USERS {
        uuid id PK
        string email UK
        string password_hash
        enum status
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        json preferences
    }

    USER_PROFILES {
        uuid id PK
        uuid user_id FK
        string first_name
        string last_name
        string display_name
        text bio
        string avatar_url
        string phone
        json social_links
        timestamp created_at
        timestamp updated_at
    }

    ALUMNI_PROFILES {
        uuid id PK
        uuid user_profile_id FK
        string student_id UK
        date graduation_date
        string degree_type
        string major
        string minor
        decimal gpa
        enum alumni_status
        enum mentor_status
        uuid domain_id FK
        json custom_fields
        timestamp created_at
        timestamp updated_at
    }

    POSTS {
        uuid id PK
        uuid author_id FK
        string title
        text content
        enum post_type
        enum visibility
        json tags
        integer like_count
        integer comment_count
        integer share_count
        boolean is_pinned
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }

    EVENTS {
        uuid id PK
        uuid organizer_id FK
        string title
        text description
        datetime start_time
        datetime end_time
        string location
        enum event_type
        integer capacity
        timestamp created_at
    }

    CONNECTIONS {
        uuid id PK
        uuid user_id_1 FK
        uuid user_id_2 FK
        enum status
        timestamp requested_at
        timestamp accepted_at
    }

    JOB_POSTINGS {
        uuid id PK
        uuid creator_id FK
        string title
        text description
        string company
        string location
        enum job_type
        decimal salary_min
        decimal salary_max
        timestamp posted_at
        timestamp expires_at
    }

    CONVERSATIONS {
        uuid id PK
        string name
        text description
        boolean is_group
        uuid creator_id FK
        timestamp created_at
        timestamp last_activity
    }

    MESSAGES {
        uuid id PK
        uuid conversation_id FK
        uuid sender_id FK
        text content
        enum message_type
        enum status
        boolean is_encrypted
        timestamp sent_at
        timestamp delivered_at
        timestamp read_at
    }

    SKILLS {
        uuid id PK
        string name UK
        string category
        boolean is_active
        timestamp created_at
    }
    </div>
</body>
</html>