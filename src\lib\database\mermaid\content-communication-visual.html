<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content & Communication Database Schema</title>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            transition: background-color 0.3s, color 0.3s;
        }
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        /* Dark mode for Mermaid */
        :root {
            --mermaid-bg: #1a1a1a;
            --mermaid-node-bg: #2d2d2d;
            --mermaid-node-border: #555555;
            --mermaid-text-color: #ffffff;
            --mermaid-edge-color: #888888;
            --mermaid-label-bg: #333333;
        }
    </style>
</head>
<body>
    <h1>Content & Communication Database Schema</h1>
    <div class="mermaid">
erDiagram
    %% Content & Communication
    POSTS {
        uuid id PK
        uuid author_id FK
        string title
        text content
        enum post_type
        enum visibility
        json tags
        integer like_count
        integer comment_count
        integer share_count
        boolean is_pinned
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }
    COMMENTS {
        uuid id PK
        uuid post_id FK
        uuid author_id FK
        uuid parent_comment_id FK
        text content
        integer like_count
        boolean is_edited
        timestamp created_at
        timestamp updated_at
    }
    POST_ATTACHMENTS {
        uuid id PK
        uuid post_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }
    CONVERSATIONS {
        uuid id PK
        string name
        text description
        boolean is_group
        uuid creator_id FK
        timestamp created_at
        timestamp last_activity
    }
    CONVERSATION_PARTICIPANTS {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        enum role
        timestamp joined_at
        boolean is_active
    }
    MESSAGES {
        uuid id PK
        uuid conversation_id FK
        uuid sender_id FK
        text content
        enum message_type
        enum status
        boolean is_encrypted
        timestamp sent_at
        timestamp delivered_at
        timestamp read_at
    }
    MESSAGE_ATTACHMENTS {
        uuid id PK
        uuid message_id FK
        string file_name
        string file_type
        string file_url
        integer file_size
        json metadata
        timestamp uploaded_at
    }
    USER_TYPING {
        uuid id PK
        uuid conversation_id FK
        uuid user_id FK
        timestamp started_at
        timestamp last_update
    }
    NOTIFICATIONS {
        uuid id PK
        uuid user_id FK
        string type
        json data
        boolean is_read
        timestamp created_at
    }

    %% Relationships
    USERS ||--o{ POSTS : creates
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ CONVERSATIONS : creates
    USERS ||--o{ CONVERSATION_PARTICIPANTS : participates_in
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ USER_TYPING : indicates_typing
    USERS ||--o{ NOTIFICATIONS : receives
    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : has
    CONVERSATIONS ||--o{ MESSAGES : contains
    CONVERSATIONS ||--o{ USER_TYPING : tracks_typing
    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : contains
    POSTS ||--o{ COMMENTS : has
    POSTS ||--o{ POST_ATTACHMENTS : contains
    </div>
</body>
</html>